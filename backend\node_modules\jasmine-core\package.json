{"name": "jasmine-core", "license": "MIT", "version": "5.8.0", "repository": {"type": "git", "url": "https://github.com/jasmine/jasmine.git"}, "keywords": ["test", "testing", "jasmine", "tdd", "bdd"], "scripts": {"posttest": "eslint \"src/**/*.js\" \"spec/**/*.js\" && prettier --check \"src/**/*.js\" \"spec/**/*.js\"", "test": "node scripts/runSpecsInNode.js", "test:parallel": "node scripts/runSpecsInParallel.js", "cleanup": "prettier --write \"src/**/*.js\" \"spec/**/*.js\"", "build": "node scripts/buildDistribution.js", "buildStandaloneDist": "node scripts/buildStandaloneDist.js", "serve": "node spec/support/localJasmineBrowser.js", "serve:performance": "node spec/support/localJasmineBrowser.js jasmine-browser-performance.json", "ci": "node spec/support/ci.js", "ci:performance": "node spec/support/ci.js jasmine-browser-performance.json"}, "description": "Simple JavaScript testing framework for browsers and node.js", "homepage": "https://jasmine.github.io", "main": "./lib/jasmine-core.js", "files": ["LICENSE", "README.md", "images/*.{png,svg}", "lib/**/*.{js,css}", "package.json"], "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "archiver": "^7.0.1", "css-url-embed": "^0.1.0", "ejs": "^3.1.10", "eslint": "^9.24.0", "eslint-plugin-compat": "^6.0.2", "glob": "^10.2.3", "globals": "^16.0.0", "jasmine": "^5.0.0", "jasmine-browser-runner": "github:jasmine/jasmine-browser-runner", "jsdom": "^26.0.0", "prettier": "1.17.1", "rimraf": "^5.0.10", "sass": "^1.58.3", "shelljs": "^0.9.2"}, "browserslist": ["Safari >= 15", "Firefox >= 102", "last 2 Chrome versions", "last 2 Edge versions"]}