{"name": "karma", "description": "Spectacular Test Runner for JavaScript.", "homepage": "https://karma-runner.github.io/", "repository": {"type": "git", "url": "git://github.com/karma-runner/karma.git"}, "bugs": {"url": "https://github.com/karma-runner/karma/issues"}, "keywords": ["karma", "spectacular", "runner", "karma", "js", "javascript", "testing", "test", "remote", "execution"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON>yer <<EMAIL>>", "dignifiedquire <<EMAIL>>", "johnj<PERSON>ton <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "greenkeeperio-bot <<EMAIL>>", "semantic-release-bot <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "ukasz Usarz <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "taichi <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "lukasz <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Ciro Nunes <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Robo <<EMAIL>>", "<PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <nmala<PERSON><PERSON>@palantir.com>", "falsandtru <<EMAIL>>", "joshjb84 <<EMAIL>>", "vivganes <<EMAIL>>", "<PERSON> <<EMAIL>>", "Aymeric <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Iristyle <Iristyle@github>", "<PERSON> <<EMAIL>>", "<PERSON> <jeff<PERSON><EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <jansen<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "dependabot[bot] <49699333+dependabot[bot]@users.noreply.github.com>", "pavelgj <<EMAIL>>", "sylvain-hamel <<EMAIL>>", "ywong <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> (陳昌倬) <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Greenkeeper <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <kevin<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Parashuram <<EMAIL>>", "<PERSON> <<EMAIL>>", "PatrickJS <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>ke Gaskill <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <ma<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "SymbioticKilla <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <i<PERSON><PERSON><PERSON>@users.noreply.github.com>", "comdiv <fagim.sad<PERSON><PERSON>@gmail.com>", "karmarunnerbot <<EMAIL>>", "ngiebel <<EMAIL>>", "rdodev <<EMAIL>>", "u812 <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <a.krumms<PERSON>@litixsoft.de>", "<PERSON> <a.p<PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <and<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Atul Bhosale <<EMAIL>>", "AugustinLF <<EMAIL>>", "AvnerCohen <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Basemm <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Borewit <<EMAIL>>", "<PERSON> Wied <<EMAIL>>", "<PERSON> <<EMAIL>>", "Breno Cal<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <xrda<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Chuf <<EMAIL>>", "Ciro S. Costa <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <david@frode.(none)>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <pair+db<PERSON><PERSON>@vmware.com>", "<PERSON><PERSON> <<EMAIL>>", "ERt <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Eden <mazzola<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Islam Sharabash <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <john<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@users.noreply.github.com>", "<PERSON>h<PERSON><PERSON> Teng <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <karol.fab<PERSON>@lemondemon.pl>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Keats <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "LoveIsGrief <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <lza<PERSON><EMAIL>>", "M1xA <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Manoel <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Niemelä <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <david<PERSON><PERSON><PERSON>@fastmail.co.uk>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Milan Aleksic <mi<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <m<PERSON><PERSON><PERSON><PERSON>@renderedtext.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "NeverwinterMoon <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <code.n<PERSON>ru<PERSON><PERSON>@gmail.com>", "<PERSON> <npetruzz<PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON> <nicola<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON>sh <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "OpenShift guest <<EMAIL>>", "Outsider <<EMAIL>>", "Parashuram <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <pedrot<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> (<PERSON>) <peter<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>", "Piper Chester <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Roarke Gaskill <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Rémi <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Si<PERSON>aj <20282546+<PERSON><PERSON><PERSON>-<PERSON>@users.noreply.github.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Thai Pangsakulyanont @ Taskworld <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> Triemstra <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <tim.olshan<PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "TrevDev <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Volune <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Wizek <<EMAIL>>", "XhmikosR <<EMAIL>>", "Yang09701194 <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> zhengzheng <<EMAIL>>", "adamnation <<EMAIL>>", "ahaurw01 <<EMAIL>>", "ashaffer <<EMAIL>>", "cexbrayat <<EMAIL>>", "coderaiser <<EMAIL>>", "compact <<EMAIL>>", "coridrew <<EMAIL>>", "cy6erskunk <<EMAIL>>", "david-garcia-nete <<EMAIL>>", "deepak1556 <<EMAIL>>", "dorey <<EMAIL>>", "grifball <<EMAIL>>", "hdmr14 <<EMAIL>>", "hrgdavor <<EMAIL>>", "ianjobling <<EMAIL>>", "inf3rno <<EMAIL>>", "is-already-taken <<EMAIL>>", "jjoos <<EMAIL>>", "jvalkeejarvi <<EMAIL>>", "katrina95 <<EMAIL>>", "kyo_ago <<EMAIL>>", "lanshunfang <<EMAIL>>", "lusarz <<EMAIL>>", "maik <<EMAIL>>", "mdemo <<EMAIL>>", "nathanfaucett <<EMAIL>>", "pardoman <<EMAIL>>", "sharmanikhil04 <<EMAIL>>", "thetrevdev <<EMAIL>>", "thorn0 <<EMAIL>>", "toran billups <<EMAIL>>", "xel23 <<EMAIL>>", "<EMAIL>>", "<EMAIL>>"], "dependencies": {"@colors/colors": "1.5.0", "body-parser": "^1.19.0", "braces": "^3.0.2", "chokidar": "^3.5.1", "connect": "^3.7.0", "di": "^0.0.1", "dom-serialize": "^2.2.1", "glob": "^7.1.7", "graceful-fs": "^4.2.6", "http-proxy": "^1.18.1", "isbinaryfile": "^4.0.8", "lodash": "^4.17.21", "log4js": "^6.4.1", "mime": "^2.5.2", "minimatch": "^3.0.4", "mkdirp": "^0.5.5", "qjobs": "^1.2.0", "range-parser": "^1.2.1", "rimraf": "^3.0.2", "socket.io": "^4.7.2", "source-map": "^0.6.1", "tmp": "^0.2.1", "ua-parser-js": "^0.7.30", "yargs": "^16.1.1"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-angular": "^12.1.4", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.1", "browserify": "^16.2.3", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-subset": "^1.2.2", "cucumber": "^6.0.5", "eslint": "^7.6.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "http2": "^3.3.6", "jasmine-core": "^3.6.0", "karma": ".", "karma-browserify": "^7.0.0", "karma-browserstack-launcher": "^1.6.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.3.0", "karma-jasmine": "^2.0.1", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^1.0.1", "karma-mocha-reporter": "^2.0.0", "karma-script-launcher": "^1.0.0", "mocha": "^4.1.0", "mocks": "^0.0.15", "proxyquire": "^2.1.3", "puppeteer": "^1.20.0", "semantic-release": "^17.4.7", "sinon": "7.3.2", "sinon-chai": "^3.5.0", "supertest": "^4.0.2", "timer-shim": "^0.3.0", "watchify": "^3.11.1", "which": "^1.3.1"}, "main": "./lib/index", "bin": {"karma": "./bin/karma"}, "engines": {"node": ">= 10"}, "version": "6.4.4", "license": "MIT", "scripts": {"lint": "eslint . --ext js --ignore-pattern *.tpl.js", "lint:fix": "eslint . --ext js --ignore-pattern *.tpl.js --fix", "commit:check": "commitlint --from HEAD~1", "test:unit": "mocha \"test/unit/**/*.spec.js\"", "test:e2e": "cucumber-js test/e2e/*.feature", "test:client": "node bin/karma start test/client/karma.conf.js", "test": "npm run test:unit && npm run test:e2e && npm run test:client", "build": "node scripts/client.js build", "build:check": "node scripts/client.js check", "build:watch": "node scripts/client.js watch", "test:integration": "./scripts/integration-tests.sh", "semantic-release": "semantic-release", "commitlint": "commitlint"}}