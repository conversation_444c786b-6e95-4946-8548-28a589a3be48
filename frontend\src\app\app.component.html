<div class="app-container">
  <div class="app-container" *ngIf="authService.isAuthenticated(); else unauthenticatedView">
  <!-- Top Navigation -->
  <mat-toolbar color="primary" class="app-toolbar">
    <button mat-icon-button (click)="sidenav.toggle()" class="menu-button">
      <mat-icon>menu</mat-icon>
    </button>

    <span class="toolbar-title">Indezy</span>

    <span class="spacer"></span>

    <button mat-button [matMenuTriggerFor]="userMenu" class="user-button">
      <mat-icon>account_circle</mat-icon>
      <span class="user-name">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</span>
      <mat-icon>arrow_drop_down</mat-icon>
    </button>

    <mat-menu #userMenu="matMenu">
      <button mat-menu-item routerLink="/profile">
        <mat-icon>person</mat-icon>
        <span>Profil</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Déconnexion</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <!-- Sidenav Container -->
  <mat-sidenav-container class="app-sidenav-container">
    <mat-sidenav #sidenav mode="side" opened class="app-sidenav">
      <mat-nav-list>
        <a mat-list-item
           *ngFor="let item of menuItems"
           [routerLink]="item.route"
           routerLinkActive="active-nav-item">
          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
          <span matListItemTitle>{{ item.label }}</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <!-- Main Content -->
    <mat-sidenav-content class="app-main-content">
      <div class="app-content">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>

<ng-template #unauthenticatedView>
  <router-outlet></router-outlet>
</ng-template>
</div>
