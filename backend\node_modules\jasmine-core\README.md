<a name="READM<PERSON>"><img src="https://raw.githubusercontent.com/jasmine/jasmine/main/images/jasmine-horizontal.svg" width="400px" alt="Jasmine"></a>

# A JavaScript Testing Framework

Jasmine is a Behavior Driven Development testing framework for JavaScript. It does not rely on browsers, DOM, or any JavaScript framework. Thus it's suited for websites, [Node.js](http://nodejs.org) projects, or anywhere that JavaScript can run.

Upgrading from Jasmine 4.x? Check out the [upgrade guide](https://jasmine.github.io/tutorials/upgrading_to_Jasmine_5.0).

## Contributing

Please read the [contributors' guide](https://github.com/jasmine/jasmine/blob/main/.github/CONTRIBUTING.md).

## Installation

There are several different ways to install <PERSON>, depending on your 
environment and how you'd like to use it. See the [Getting Started page](https://jasmine.github.io/pages/getting_started.html)
for details.

## Usage

See the [documentation site](https://jasmine.github.io/pages/docs_home.html),
particularly the [Your First Suite tutorial](https://jasmine.github.io/tutorials/your_first_suite)
for information on writing specs, and [the FAQ](https://jasmine.github.io/pages/faq.html).

## Supported environments

Jasmine tests itself across popular browsers (Safari, Chrome, Firefox, and
Microsoft Edge) as well as Node.

| Environment       | Supported versions         |
|-------------------|----------------------------|
| Node              | 18, 20, 22, 24             |
| Safari            | 15*, 16*, 17*              |
| Chrome            | Evergreen                  |
| Firefox           | Evergreen, 102*, 115*, 128 |
| Edge              | Evergreen                  |

For evergreen browsers, each version of Jasmine is tested against the version of the browser that is available to us
at the time of release. Other browsers, as well as older & newer versions of some supported browsers, are likely to work.
However, Jasmine isn't tested against them and they aren't actively supported. 

\* Supported on a best-effort basis. Support for these versions may be dropped
if it becomes impractical, and bugs affecting only these versions may not be
treated as release blockers.

To find out what environments work with a particular Jasmine release, see the [release notes](https://github.com/jasmine/jasmine/tree/main/release_notes).

## Maintainers

* [Gwendolyn Van Hove](mailto:<EMAIL>)
* [Steve Gravrock](mailto:<EMAIL>)

### Maintainers Emeritus

* [Davis W. Frank](mailto:<EMAIL>)
* [Rajan Agaskar](mailto:<EMAIL>)
* [Greg Cobb](mailto:<EMAIL>)
* [Chris Amavisca](mailto:<EMAIL>)
* [Christian Williams](mailto:<EMAIL>)
* Sheel Choksi

Copyright (c) 2008-2019 Pivotal Labs<br>
Copyright (c) 2008-2025 The Jasmine developers<br>
This software is licensed under the [MIT License](https://github.com/jasmine/jasmine/blob/main/LICENSE).
